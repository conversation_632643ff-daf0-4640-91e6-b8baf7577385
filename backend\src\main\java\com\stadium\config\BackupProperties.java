package com.stadium.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 数据库备份配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "stadium.backup")
public class BackupProperties {

    /**
     * MySQL命令路径
     */
    private String mysqlPath = "mysql";

    /**
     * MySQL备份命令路径
     */
    private String mysqlDumpPath = "mysqldump";

    /**
     * 数据库主机
     */
    private String dbHost = "localhost";

    /**
     * 数据库端口
     */
    private String dbPort = "3306";

    /**
     * 数据库名称
     */
    private String dbName = "stadium_management";

    /**
     * 数据库用户名
     */
    private String dbUsername = "root";

    /**
     * 数据库密码
     */
    private String dbPassword = "";

    /**
     * 备份文件存储目录
     */
    private String backupDir = "backend/exports";
}