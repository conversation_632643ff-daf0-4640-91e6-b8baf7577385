package com.stadium.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.stadium.dto.ActivityDTO;
import com.stadium.entity.Activity;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 活动服务接口
 */
public interface ActivityService extends IService<Activity> {

    /**
     * 创建活动
     *
     * @param activityDTO 活动信息
     * @return 创建的活动
     */
    Activity createActivity(ActivityDTO activityDTO);

    /**
     * 更新活动信息
     *
     * @param id          活动ID
     * @param activityDTO 活动信息
     * @return 更新后的活动
     */
    Activity updateActivity(Long id, ActivityDTO activityDTO);

    /**
     * 删除活动
     *
     * @param id 活动ID
     */
    void deleteActivity(Long id);

    /**
     * 获取活动详情
     *
     * @param id 活动ID
     * @return 活动详情
     */
    Activity getActivity(Long id);

    /**
     * 分页查询活动列表
     *
     * @param current 当前页
     * @param size    每页大小
     * @param keyword 关键字
     * @return 活动列表
     */
    Page<Activity> listActivities(Integer current, Integer size, String keyword);

    /**
     * 发布活动
     *
     * @param id 活动ID
     */
    void publishActivity(Long id);

    /**
     * 取消活动
     *
     * @param id 活动ID
     */
    void cancelActivity(Long id);

    /**
     * 根据状态查询活动列表
     *
     * @param status 活动状态
     * @return 活动列表
     */
    List<Activity> getActivitiesByStatus(Integer status);

    /**
     * 查询指定时间范围内的活动
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 活动列表
     */
    List<Activity> getActivitiesByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 检查活动是否可报名
     *
     * @param id 活动ID
     * @return 是否可报名
     */
    boolean isActivityAvailable(Long id);

    /**
     * 更新活动状态
     *
     * @param id     活动ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateActivityStatus(Long id, Integer status);

    /**
     * 分页查询活动列表
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param name     活动名称
     * @param typeId   活动类型ID
     * @param status   活动状态
     * @return 分页活动列表
     */
    IPage<ActivityDTO> getActivityPage(Integer pageNum, Integer pageSize, String name, Long typeId, Integer status);

    /**
     * 获取活动列表
     *
     * @param typeId 活动类型ID
     * @param status 活动状态
     * @return 活动列表
     */
    List<ActivityDTO> getActivityList(Long typeId, Integer status);

    /**
     * 获取活动详情
     *
     * @param id 活动ID
     * @return 活动详情
     */
    ActivityDTO getActivityDetail(Long id);
}
