package com.stadium.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 活动分享实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("activity_share")
@Schema(description = "活动分享")
public class ActivityShare extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 活动ID
     */
    @Schema(description = "活动ID")
    private Long activityId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 分享类型：1-微信，2-朋友圈，3-微博，4-QQ
     */
    @Schema(description = "分享类型：1-微信，2-朋友圈，3-微博，4-QQ")
    private Integer shareType;

    /**
     * 分享时间
     */
    @Schema(description = "分享时间")
    private LocalDateTime shareTime;
}