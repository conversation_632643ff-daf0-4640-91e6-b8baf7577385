package com.stadium.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 活动提醒实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("activity_reminder")
@Schema(description = "活动提醒")
public class ActivityReminder extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 活动ID
     */
    @Schema(description = "活动ID")
    private Long activityId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 提醒时间
     */
    @Schema(description = "提醒时间")
    private LocalDateTime reminderTime;

    /**
     * 提醒类型：1-短信，2-邮件，3-站内信
     */
    @Schema(description = "提醒类型：1-短信，2-邮件，3-站内信")
    private Integer reminderType;

    /**
     * 状态：0-未发送，1-已发送
     */
    @Schema(description = "状态：0-未发送，1-已发送")
    private Integer status;
}