package com.stadium.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 活动收藏实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("activity_favorite")
@Schema(description = "活动收藏")
public class ActivityFavorite extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 活动ID
     */
    @Schema(description = "活动ID")
    private Long activityId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
}