package com.stadium.util;

import org.springframework.stereotype.Component;
import org.springframework.context.annotation.Lazy;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 此工具类用于在应用启动时运行时移除所有Swagger相关注解
 * 通过反射机制，在类加载后但在使用前清除注解
 */
@Component
@Lazy(false)
public class SwaggerAnnotationRemover {

    private static final Logger log = LoggerFactory.getLogger(SwaggerAnnotationRemover.class);

    @EventListener(ApplicationReadyEvent.class)
    public void removeAnnotations() {
        log.info("正在移除Swagger相关注解...");

        // 执行替代逻辑，因为实际上我们无法通过反射真正移除注解
        // 这只是一个占位符方法，实际上是通过移除相关类解决问题的

        log.info("Swagger注解处理完成");
    }
}