@echo off
chcp 65001
echo ========================================
echo 体育场馆管理系统 - 毕业设计版本
echo ========================================
echo.

echo 正在检查环境...

:: 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Java环境，请安装JDK 1.8或更高版本
    pause
    exit /b 1
)

:: 检查Node.js环境
node -v >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Node.js环境，请安装Node.js 14或更高版本
    pause
    exit /b 1
)

:: 检查MySQL服务
net start | find "MySQL" >nul
if %errorlevel% neq 0 (
    echo [警告] MySQL服务未启动，请确保MySQL服务正在运行
)

echo [信息] 环境检查完成
echo.

:: 启动后端服务
echo 正在启动后端服务...
cd backend
start "后端服务" cmd /k "mvn spring-boot:run -Dfile.encoding=UTF-8"
cd ..

:: 等待后端启动
echo 等待后端服务启动...
timeout /t 10 /nobreak >nul

:: 启动前端服务
echo 正在启动前端服务...
cd frontend
start "前端服务" cmd /k "npm run serve"
cd ..

echo.
echo ========================================
echo 启动完成！
echo 前端地址: http://localhost:8083
echo 后端地址: http://localhost:8080/api
echo ========================================
echo.
echo 默认账号信息：
echo 系统管理员: admin / 123456
echo 场馆管理员: venue / 123456
echo 财务人员: finance / 123456
echo 普通用户: user / 123456
echo.
echo 按任意键退出...
pause >nul