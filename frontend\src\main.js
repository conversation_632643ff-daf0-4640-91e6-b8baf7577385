import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
// 按需导入 Element UI 组件
import {
  Button, Form, FormItem, Input, Select, Option, Table, TableColumn,
  Pagination, Dialog, Menu, Submenu, MenuItem, Dropdown, DropdownMenu,
  DropdownItem, Card, Row, Col, Loading, Message, MessageBox, Notification
} from 'element-ui'
// 按需导入常用组件
import {
  DatePicker, TimePicker, Tabs, TabPane, Tag, Alert, Tooltip, Badge
} from 'element-ui'
// 按需导入布局组件
import {
  Container, Header, Aside, Main, Footer, Breadcrumb, BreadcrumbItem
} from 'element-ui'

// 样式导入
import '@/styles/index.scss'

// 工具导入
import axios from 'axios'
import VueAxios from 'vue-axios'
import permissionDirective from '@/directives/permission'
import formValidationDirective from '@/directives/form-validation'
import errorHandler from '@/utils/error-handler'
import apiHelper from '@/utils/api-helper'
import performanceMonitor from '@/utils/performance'

// 导入常用全局组件
import Permission from '@/components/Permission.vue'
import ErrorBoundary from '@/components/ErrorBoundary.vue'
import LoadingOverlay from '@/components/LoadingOverlay.vue'
import ActionButtons from '@/components/common/ActionButtons.vue'
import StandardTable from '@/components/common/StandardTable.vue'
import PageContainer from '@/components/common/PageContainer.vue'

Vue.config.productionTip = false

// 注册 Element UI 基础组件
Vue.use(Button)
Vue.use(Form)
Vue.use(FormItem)
Vue.use(Input)
Vue.use(Select)
Vue.use(Option)
Vue.use(Table)
Vue.use(TableColumn)
Vue.use(Pagination)
Vue.use(Dialog)
Vue.use(Menu)
Vue.use(Submenu)
Vue.use(MenuItem)
Vue.use(Dropdown)
Vue.use(DropdownMenu)
Vue.use(DropdownItem)
Vue.use(Card)
Vue.use(Row)
Vue.use(Col)

// 注册 Element UI 常用组件
Vue.use(DatePicker)
Vue.use(TimePicker)
Vue.use(Tabs)
Vue.use(TabPane)
Vue.use(Tag)
Vue.use(Alert)
Vue.use(Tooltip)
Vue.use(Badge)

// 注册 Element UI 布局组件
Vue.use(Container)
Vue.use(Header)
Vue.use(Aside)
Vue.use(Main)
Vue.use(Footer)
Vue.use(Breadcrumb)
Vue.use(BreadcrumbItem)

// 注册全局组件
Vue.component('Permission', Permission)
Vue.component('ErrorBoundary', ErrorBoundary)
Vue.component('LoadingOverlay', LoadingOverlay)
Vue.component('ActionButtons', ActionButtons)
Vue.component('StandardTable', StandardTable)
Vue.component('PageContainer', PageContainer)

// 注册全局方法
Vue.use(Loading.directive)
Vue.prototype.$loading = Loading.service
Vue.prototype.$msgbox = MessageBox
Vue.prototype.$alert = MessageBox.alert
Vue.prototype.$confirm = MessageBox.confirm
Vue.prototype.$prompt = MessageBox.prompt
Vue.prototype.$notify = Notification
Vue.prototype.$message = Message

// 使用axios
Vue.use(VueAxios, axios)

// 注册权限指令
Vue.directive('permission', permissionDirective)

// 注册表单验证指令
Vue.directive('form-validation', formValidationDirective)

// 注册错误处理函数
Vue.prototype.$errorHandler = errorHandler

// 初始化全局错误处理器
errorHandler.initGlobalErrorHandler()

// 注册API辅助函数
Vue.prototype.$api = apiHelper

// 配置axios基础URL
axios.defaults.baseURL = process.env.VUE_APP_API_BASE_URL || '/api'

// 注意：axios拦截器已在utils/request.js中配置，这里不再重复配置
// 全局使用的axios实例应该从utils/request.js导入

// 初始化性能监控
performanceMonitor.init()

// 在开发环境下，将性能监控工具暴露到全局，方便调试
if (process.env.NODE_ENV === 'development') {
  // @ts-ignore
  window.$performance = performanceMonitor
}

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
