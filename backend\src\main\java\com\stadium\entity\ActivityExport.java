package com.stadium.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 活动导出记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("activity_export")
@Schema(description = "活动导出记录")
public class ActivityExport extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 活动ID
     */
    @Schema(description = "活动ID")
    private Long activityId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 导出类型：1-报名名单，2-签到记录，3-评价记录
     */
    @Schema(description = "导出类型：1-报名名单，2-签到记录，3-评价记录")
    private Integer exportType;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径")
    private String filePath;

    /**
     * 文件大小(字节)
     */
    @Schema(description = "文件大小(字节)")
    private Long fileSize;

    /**
     * 状态：0-导出中，1-已完成，2-失败
     */
    @Schema(description = "状态：0-导出中，1-已完成，2-失败")
    private Integer status;
}